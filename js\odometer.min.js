/*! odometer 0.4.5 */
(function(){var t,e,n,i,o,r,s,a,u,l,d,h,p,c,m,f,g,v,w,M,y,b,T=[].slice;e='<span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner">'+('<span class="odometer-ribbon"><span class="odometer-ribbon-inner">'+(s='<span class="odometer-value"></span>')+"</span></span>")+"</span></span>",t="(,ddd).dd",n=/^\(?([^)]*)\)?(?:(.)(d+))?$/,m=document.createElement("div").style,r=null!=m.transition||null!=m.webkitTransition||null!=m.mozTransition||null!=m.oTransition,p=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame,i=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver,u=function(t){var e;return(e=document.createElement("div")).innerHTML=t,e.children[0]},h=function(t,e){return t.className=t.className.replace(new RegExp("(^| )"+e.split(" ").join("|")+"( |$)","gi")," ")},a=function(t,e){return h(t,e),t.className+=" "+e},f=function(t,e){var n;return null!=document.createEvent?((n=document.createEvent("HTMLEvents")).initEvent(e,!0,!0),t.dispatchEvent(n)):void 0},d=function(){var t,e;return null!=(t=null!=(e=window.performance)&&"function"==typeof e.now?e.now():void 0)?t:+new Date},c=function(t,e){return null==e&&(e=0),e?(t*=Math.pow(10,e),t+=.5,t=Math.floor(t),t/=Math.pow(10,e)):Math.round(t)},g=function(t){return 0>t?Math.ceil(t):Math.floor(t)},l=function(t){return t-c(t)},w=!1,(v=function(){var t,e,n,i,o;if(!w&&null!=window.jQuery){for(w=!0,o=[],e=0,n=(i=["html","text"]).length;n>e;e++)t=i[e],o.push(function(t){var e;return e=window.jQuery.fn[t],window.jQuery.fn[t]=function(t){var n;return null==t||null==(null!=(n=this[0])?n.odometer:void 0)?e.apply(this,arguments):this[0].odometer.update(t)}}(t));return o}})(),setTimeout(v,0),o=function(){function o(t){var e,n,i,r,a,u,l,d,h,p=this;if(this.options=t,this.el=this.options.el,null!=this.el.odometer)return this.el.odometer;for(e in this.el.odometer=this,l=o.options)i=l[e],null==this.options[e]&&(this.options[e]=i);null==(r=this.options).duration&&(r.duration=2e3),this.MAX_VALUES=0|this.options.duration/33.333333333333336/2,this.resetFormat(),this.value=this.cleanValue(null!=(d=this.options.value)?d:""),this.renderInside(),this.render();try{for(a=0,u=(h=["innerHTML","innerText","textContent"]).length;u>a;a++)n=h[a],null!=this.el[n]&&function(t){Object.defineProperty(p.el,t,{get:function(){var e;return"innerHTML"===t?p.inside.outerHTML:null!=(e=p.inside.innerText)?e:p.inside.textContent},set:function(t){return p.update(t)}})}(n)}catch(s){s,this.watchForMutations()}}return o.prototype.renderInside=function(){return this.inside=document.createElement("div"),this.inside.className="odometer-inside",this.el.innerHTML="",this.el.appendChild(this.inside)},o.prototype.watchForMutations=function(){var e=this;if(null!=i)try{return null==this.observer&&(this.observer=new i((function(){var t;return t=e.el.innerText,e.renderInside(),e.render(e.value),e.update(t)}))),this.watchMutations=!0,this.startWatchingMutations()}catch(t){t}},o.prototype.startWatchingMutations=function(){return this.watchMutations?this.observer.observe(this.el,{childList:!0}):void 0},o.prototype.stopWatchingMutations=function(){var t;return null!=(t=this.observer)?t.disconnect():void 0},o.prototype.cleanValue=function(t){var e;return"string"==typeof t&&(t=(t=(t=t.replace(null!=(e=this.format.radix)?e:".","<radix>")).replace(/[.,]/g,"")).replace("<radix>","."),t=parseFloat(t,10)||0),c(t,this.format.precision)},o.prototype.bindTransitionEnd=function(){var t,e,n,i,o,r,s=this;if(!this.transitionEndBound){for(this.transitionEndBound=!0,e=!1,r=[],n=0,i=(o="transitionend webkitTransitionEnd oTransitionEnd otransitionend MSTransitionEnd".split(" ")).length;i>n;n++)t=o[n],r.push(this.el.addEventListener(t,(function(){return e||(e=!0,setTimeout((function(){return s.render(),e=!1,f(s.el,"odometerdone")}),0)),!0}),!1));return r}},o.prototype.resetFormat=function(){var e,i,o,r,s,a,u,l;if((e=null!=(u=this.options.format)?u:t)||(e="d"),!(o=n.exec(e)))throw new Error("Odometer: Unparsable digit format");return a=(l=o.slice(1,4))[0],s=l[1],r=(null!=(i=l[2])?i.length:void 0)||0,this.format={repeating:a,radix:s,precision:r}},o.prototype.render=function(t){var e,n,i,o,s,a,u,d,h,p,c,m;for(null==t&&(t=this.value),this.stopWatchingMutations(),this.resetFormat(),this.inside.innerHTML="",a=this.options.theme,s=[],d=0,p=(e=this.el.className.split(" ")).length;p>d;d++)(n=e[d]).length&&((o=/^odometer-theme-(.+)$/.exec(n))?a=o[1]:/^odometer(-|$)/.test(n)||s.push(n));for(s.push("odometer"),r||s.push("odometer-no-transitions"),a?s.push("odometer-theme-"+a):s.push("odometer-auto-theme"),this.el.className=s.join(" "),this.ribbons={},this.digits=[],u=!this.format.precision||!l(t)||!1,h=0,c=(m=t.toString().split("").reverse()).length;c>h;h++)"."===(i=m[h])&&(u=!0),this.addDigit(i,u);return this.startWatchingMutations()},o.prototype.update=function(t){var e,n=this;return(e=(t=this.cleanValue(t))-this.value)?(h(this.el,"odometer-animating-up odometer-animating-down odometer-animating"),a(this.el,e>0?"odometer-animating-up":"odometer-animating-down"),this.stopWatchingMutations(),this.animate(t),this.startWatchingMutations(),setTimeout((function(){return n.el.offsetHeight,a(n.el,"odometer-animating")}),0),this.value=t):void 0},o.prototype.renderDigit=function(){return u(e)},o.prototype.insertDigit=function(t,e){return null!=e?this.inside.insertBefore(t,e):this.inside.children.length?this.inside.insertBefore(t,this.inside.children[0]):this.inside.appendChild(t)},o.prototype.addSpacer=function(t,e,n){var i;return(i=u('<span class="odometer-formatting-mark"></span>')).innerHTML=t,n&&a(i,n),this.insertDigit(i,e)},o.prototype.addDigit=function(t,e){var n,i,o,r;if(null==e&&(e=!0),"-"===t)return this.addSpacer(t,null,"odometer-negation-mark");if("."===t)return this.addSpacer(null!=(r=this.format.radix)?r:".",null,"odometer-radix-mark");if(e)for(o=!1;;){if(!this.format.repeating.length){if(o)throw new Error("Bad odometer format without digits");this.resetFormat(),o=!0}if(n=this.format.repeating[this.format.repeating.length-1],this.format.repeating=this.format.repeating.substring(0,this.format.repeating.length-1),"d"===n)break;this.addSpacer(n)}return(i=this.renderDigit()).querySelector(".odometer-value").innerHTML=t,this.digits.push(i),this.insertDigit(i)},o.prototype.animate=function(t){return r&&"count"!==this.options.animation?this.animateSlide(t):this.animateCount(t)},o.prototype.animateCount=function(t){var e,n,i,o,r,s=this;if(n=+t-this.value)return o=i=d(),e=this.value,(r=function(){var a,u;return d()-o>s.options.duration?(s.value=t,s.render(),void f(s.el,"odometerdone")):((a=d()-i)>50&&(i=d(),u=a/s.options.duration,e+=n*u,s.render(Math.round(e))),null!=p?p(r):setTimeout(r,50))})()},o.prototype.getDigitCount=function(){var t,e,n,i,o,r;for(t=o=0,r=(i=1<=arguments.length?T.call(arguments,0):[]).length;r>o;t=++o)n=i[t],i[t]=Math.abs(n);return e=Math.max.apply(Math,i),Math.ceil(Math.log(e+1)/Math.log(10))},o.prototype.getFractionalDigitCount=function(){var t,e,n,i,o,r,s;for(e=/^\-?\d*\.(\d*?)0*$/,t=r=0,s=(o=1<=arguments.length?T.call(arguments,0):[]).length;s>r;t=++r)i=o[t],o[t]=i.toString(),n=e.exec(o[t]),o[t]=null==n?0:n[1].length;return Math.max.apply(Math,o)},o.prototype.resetDigits=function(){return this.digits=[],this.ribbons=[],this.inside.innerHTML="",this.resetFormat()},o.prototype.animateSlide=function(t){var e,n,i,o,r,s,u,l,d,h,p,c,m,f,v,w,M,y,b,T,E,x,S,L,D,A,C;if(w=this.value,(l=this.getFractionalDigitCount(w,t))&&(t*=Math.pow(10,l),w*=Math.pow(10,l)),i=t-w){for(this.bindTransitionEnd(),o=this.getDigitCount(w,t),r=[],e=0,p=b=0;o>=0?o>b:b>o;p=o>=0?++b:--b){if(M=g(w/Math.pow(10,o-p-1)),s=(u=g(t/Math.pow(10,o-p-1)))-M,Math.abs(s)>this.MAX_VALUES){for(h=[],c=s/(this.MAX_VALUES+this.MAX_VALUES*e*.5),n=M;s>0&&u>n||0>s&&n>u;)h.push(Math.round(n)),n+=c;h[h.length-1]!==u&&h.push(u),e++}else h=function(){C=[];for(var t=M;u>=M?u>=t:t>=u;u>=M?t++:t--)C.push(t);return C}.apply(this);for(p=T=0,x=h.length;x>T;p=++T)d=h[p],h[p]=Math.abs(d%10);r.push(h)}for(this.resetDigits(),p=E=0,S=(A=r.reverse()).length;S>E;p=++E)for(h=A[p],this.digits[p]||this.addDigit(" ",p>=l),null==(y=this.ribbons)[p]&&(y[p]=this.digits[p].querySelector(".odometer-ribbon-inner")),this.ribbons[p].innerHTML="",0>i&&(h=h.reverse()),m=D=0,L=h.length;L>D;m=++D)d=h[m],(v=document.createElement("div")).className="odometer-value",v.innerHTML=d,this.ribbons[p].appendChild(v),m===h.length-1&&a(v,"odometer-last-value"),0===m&&a(v,"odometer-first-value");return 0>M&&this.addDigit("-"),null!=(f=this.inside.querySelector(".odometer-radix-mark"))&&f.parent.removeChild(f),l?this.addSpacer(this.format.radix,this.digits[l-1],"odometer-radix-mark"):void 0}},o}(),o.options=null!=(y=window.odometerOptions)?y:{},setTimeout((function(){var t,e,n,i,r;if(window.odometerOptions){for(t in r=[],i=window.odometerOptions)e=i[t],r.push(null!=(n=o.options)[t]?(n=o.options)[t]:n[t]=e);return r}}),0),o.init=function(){var t,e,n,i,r,s;if(null!=document.querySelectorAll){for(s=[],n=0,i=(e=document.querySelectorAll(o.options.selector||".odometer")).length;i>n;n++)t=e[n],s.push(t.odometer=new o({el:t,value:null!=(r=t.innerText)?r:t.textContent}));return s}},null!=(null!=(b=document.documentElement)?b.doScroll:void 0)&&null!=document.createEventObject?(M=document.onreadystatechange,document.onreadystatechange=function(){return"complete"===document.readyState&&!1!==o.options.auto&&o.init(),null!=M?M.apply(this,arguments):void 0}):document.addEventListener("DOMContentLoaded",(function(){return!1!==o.options.auto?o.init():void 0}),!1),window.Odometer=o}).call(this);