<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Mobile Chat - Grace AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            display: flex;
            gap: 40px;
            max-width: 1200px;
            width: 100%;
            align-items: center;
        }

        .info-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .info-card h1 {
            font-size: 28px;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        .info-card p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .highlight {
            color: #ff6b6b;
            font-weight: 600;
        }

        .cta-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: #ff6b6b;
            border: 2px solid #ff6b6b;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-secondary:hover {
            background: #ff6b6b;
            color: white;
        }

        .mobile-frame {
            width: 300px;
            height: 600px;
            background: #000;
            border-radius: 30px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .mobile-screen {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            border-radius: 22px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            background: #f8f9fa;
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
        }

        .chat-header {
            background: #007AFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            font-size: 18px;
            cursor: pointer;
        }

        .contact-info {
            flex: 1;
            text-align: center;
        }

        .contact-name {
            font-weight: 600;
            font-size: 16px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .message.sent {
            flex-direction: row-reverse;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.received .message-bubble {
            background: #e9ecef;
            color: #333;
            border-bottom-left-radius: 4px;
        }

        .message.sent .message-bubble {
            background: #007AFF;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #007AFF;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .chat-input {
            background: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            border-top: 1px solid #e9ecef;
        }

        .input-field {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 14px;
            outline: none;
        }

        .send-btn {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: #e9ecef;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                gap: 20px;
            }
            
            .info-card {
                order: 2;
                padding: 30px;
            }
            
            .mobile-frame {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="info-card">
            <h1>Your Leads Are Messaging You. Are You Replying in Time?</h1>
            <p>Most leads go cold in under <span class="highlight">10 minutes</span>. Grace responds <span class="highlight">instantly</span>. Turning interest into booked calls and paying clients, 24/7.</p>
            
            <div class="cta-buttons">
                <button class="btn-primary" onclick="startDemo()">
                    BOOK MY FREE AI DEMO
                    <br><small style="font-size: 12px; opacity: 0.9;">Takes 30 seconds. No credit card required.</small>
                </button>
                <button class="btn-secondary" onclick="tryGrace()">TRY GRACE FIRST</button>
            </div>
            
            <p style="margin-top: 20px; font-size: 14px; color: #999;">
                Helping businesses like yours turn leads into booked calls — every single day.
            </p>
        </div>

        <div class="mobile-frame">
            <div class="mobile-screen">
                <div class="status-bar">
                    <span>15:42</span>
                    <span>📶 📶 📶 🔋</span>
                </div>
                
                <div class="chat-header">
                    <span class="back-btn">‹</span>
                    <div class="contact-info">
                        <div class="contact-name">Grace</div>
                    </div>
                    <span>📞</span>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="message received">
                        <div class="avatar">G</div>
                        <div class="message-bubble">
                            Hello! I'm Grace, your friendly AI assistant. I'm here to help you see how your own custom AI assistant can work for your business. To get started, could you share your first name with me?
                        </div>
                    </div>
                </div>
                
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
                
                <div class="chat-input">
                    <input type="text" class="input-field" id="messageInput" placeholder="Type a message..." onkeypress="handleKeyPress(event)">
                    <button class="send-btn" onclick="sendMessage()">➤</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // OpenRouter API configuration
        const OPENROUTER_API_KEY = 'sk-or-v1-your-api-key-here'; // Replace with your OpenRouter API key
        const API_URL = 'https://openrouter.ai/api/v1/chat/completions';
        
        let conversationHistory = [
            {
                role: "system",
                content: "You are Grace, a friendly AI assistant helping businesses understand how AI can work for them. Keep responses conversational, helpful, and focused on business benefits. Keep responses under 100 words."
            }
        ];

        function addMessage(content, isUser = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'sent' : 'received'}`;
            
            if (!isUser) {
                messageDiv.innerHTML = `
                    <div class="avatar">G</div>
                    <div class="message-bubble">${content}</div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="message-bubble">${content}</div>
                `;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        async function getAIResponse(userMessage) {
            try {
                conversationHistory.push({
                    role: "user",
                    content: userMessage
                });

                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': window.location.href,
                        'X-Title': 'Grace AI Chat Demo'
                    },
                    body: JSON.stringify({
                        model: "microsoft/phi-3-mini-128k-instruct:free",
                        messages: conversationHistory,
                        max_tokens: 150,
                        temperature: 0.7
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                const aiResponse = data.choices[0].message.content;
                
                conversationHistory.push({
                    role: "assistant",
                    content: aiResponse
                });

                return aiResponse;
            } catch (error) {
                console.error('Error getting AI response:', error);
                return "I'm having trouble connecting right now. Could you try again in a moment?";
            }
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage(message, true);
            input.value = '';
            
            // Show typing indicator
            showTypingIndicator();
            
            // Get AI response
            const aiResponse = await getAIResponse(message);
            
            // Hide typing indicator and add AI response
            hideTypingIndicator();
            setTimeout(() => {
                addMessage(aiResponse);
            }, 500);
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function startDemo() {
            alert('Demo booking feature would redirect to your booking system!');
        }

        function tryGrace() {
            document.getElementById('messageInput').focus();
            addMessage("Great! You can start chatting with me right here. What would you like to know about AI assistants for your business?");
        }

        // Initialize with a welcome message
        window.onload = function() {
            // The initial message is already in the HTML
        };
    </script>
</body>
</html>
